<script lang="ts" setup>
import type { ChartButtonProps, ChartButtonEmits } from './types';

import { ref } from 'vue';
import { globalShareState } from '@vben/common-ui';
import ChartModal from './components/ChartModal.vue';

defineOptions({
  name: 'ChartButton',
});

const props = withDefaults(defineProps<ChartButtonProps>(), {
  text: '执行操作',
  size: 'default',
  disabled: false,
  buttonClass: '',
  chartTitle: '负载率曲线',
  chartType: 'loadRate',
});

const emit = defineEmits<ChartButtonEmits>();

// 获取adapter中注册的组件
const { PrimaryButton } = globalShareState.getComponents();

// Modal显示状态
const modalVisible = ref(false);

// 按钮点击处理
const handleButtonClick = () => {
  emit('click');
  modalVisible.value = true;
  emit('chart-opened');
};

// Modal事件处理
const handleModalConfirm = () => {
  modalVisible.value = false;
  emit('chart-closed');
};

const handleModalCancel = () => {
  modalVisible.value = false;
  emit('chart-closed');
};

// 暴露方法
const openChart = () => {
  modalVisible.value = true;
  emit('chart-opened');
};

const closeChart = () => {
  modalVisible.value = false;
  emit('chart-closed');
};

defineExpose({
  openChart,
  closeChart,
});
</script>

<template>
  <div class="chart-button">
    <!-- 按钮 -->
    <PrimaryButton
      :size="size"
      :disabled="disabled"
      :class="buttonClass"
      @click="handleButtonClick"
    >
      {{ text }}
    </PrimaryButton>

    <!-- 图表弹窗 -->
    <ChartModal
      v-model:visible="modalVisible"
      :title="chartTitle"
      :chart-type="chartType"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    />
  </div>
</template>

<style scoped>
.chart-button {
  display: inline-block;
}
</style>
