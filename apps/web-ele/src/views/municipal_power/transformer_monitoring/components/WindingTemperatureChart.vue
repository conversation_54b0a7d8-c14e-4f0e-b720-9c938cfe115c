<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 生成24小时的时间数据
const generateTimeData = () => {
  const times = [];
  for (let i = 0; i < 24; i++) {
    times.push(`${i.toString().padStart(2, '0')}:00`);
  }
  return times;
};

// 生成绕组温度数据（三相）
const generateTemperatureData = () => {
  const baseTemp = 65; // 基础温度 °C
  const dataA = [];
  const dataB = [];
  const dataC = [];

  for (let i = 0; i < 24; i++) {
    let tempVariation;
    if (i >= 6 && i <= 8) {
      // 早高峰，温度上升
      tempVariation = Math.random() * 8 + 5;
    } else if (i >= 18 && i <= 22) {
      // 晚高峰，温度较高
      tempVariation = Math.random() * 10 + 8;
    } else if (i >= 0 && i <= 5) {
      // 深夜，温度较低
      tempVariation = Math.random() * 4 - 2;
    } else {
      // 其他时间
      tempVariation = Math.random() * 6;
    }

    // A相温度（稍高）
    dataA.push(Math.max(45, Math.min(85, baseTemp + tempVariation + Math.random() * 2)));

    // B相温度（中等）
    dataB.push(Math.max(45, Math.min(85, baseTemp + tempVariation + (Math.random() - 0.5) * 2)));

    // C相温度（稍低）
    dataC.push(Math.max(45, Math.min(85, baseTemp + tempVariation - Math.random() * 2)));
  }

  return { dataA, dataB, dataC };
};

onMounted(() => {
  const timeData = generateTimeData();
  const { dataA, dataB, dataC } = generateTemperatureData();

  renderEcharts({
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片',
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
        restore: {
          title: '还原',
        },
        dataView: {
          title: '数据视图',
          readOnly: false,
        },
      },
      right: '3%',
      top: '8%',
    },
    grid: {
      bottom: '20%',
      containLabel: true,
      left: '0%',
      right: '0%',
      top: '25%',
    },
    legend: {
      bottom: '5%',
      data: ['A相绕组', 'B相绕组', 'C相绕组'],
      textStyle: {
        fontSize: 11,
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#eab308',
          width: 1,
        },
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value.toFixed(1)}°C<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: timeData,
      type: 'category',
      axisLabel: {
        fontSize: 11,
        interval: 2, // 每隔2个显示一个标签
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        formatter: '{value}°C',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
      min: 40,
      max: 90,
    },
    series: [
      {
        name: 'A相绕组',
        data: dataA,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#eab308', // yellow-500
          width: 2,
        },
        itemStyle: {
          color: '#eab308',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: 'B相绕组',
        data: dataB,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#22c55e', // green-500
          width: 2,
        },
        itemStyle: {
          color: '#22c55e',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: 'C相绕组',
        data: dataC,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#ef4444', // red-500
          width: 2,
        },
        itemStyle: {
          color: '#ef4444',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
    ],
  });
});
</script>

<template>
  <div class="h-full w-full">
    <EchartsUI ref="chartRef" height="100%" />
  </div>
</template>
