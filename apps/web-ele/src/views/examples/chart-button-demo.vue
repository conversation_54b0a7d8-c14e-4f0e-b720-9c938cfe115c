<script lang="ts" setup>
import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { ElCard, ElMessage, ElButton } from 'element-plus';
import ChartButton from '#/components/services/chart-modal/index.vue';

defineOptions({
  name: 'ChartButtonDemo',
});

// 图表按钮引用
const chartButtonRef = ref<InstanceType<typeof ChartButton>>();

// 事件处理
const handleChartOpened = () => {
  console.log('图表弹窗已打开');
  ElMessage.success('图表弹窗已打开');
};

const handleChartClosed = () => {
  console.log('图表弹窗已关闭');
  ElMessage.info('图表弹窗已关闭');
};

const handleButtonClick = () => {
  console.log('按钮被点击');
};

// 程序化打开图表
const openChart = () => {
  chartButtonRef.value?.openChart();
};

// 程序化关闭图表
const closeChart = () => {
  chartButtonRef.value?.closeChart();
};
</script>

<template>
  <Page
    description="基于VbenModal封装的图表按钮组件演示，点击按钮弹出图表弹窗"
    title="图表按钮演示"
  >
    <div class="space-y-6">
      <!-- 基础使用 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">基础使用</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <ChartButton
              ref="chartButtonRef"
              text="查看负载率曲线"
              chart-title="负载率分析"
              chart-type="loadRate"
              @click="handleButtonClick"
              @chart-opened="handleChartOpened"
              @chart-closed="handleChartClosed"
            />
          </div>

          <div class="text-sm text-gray-600">
            点击按钮打开图表弹窗，支持日期选择、指标切换、连线/数据显示控制
          </div>
        </div>
      </ElCard>

      <!-- 不同类型 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">不同图表类型</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <ChartButton
              text="负载率"
              chart-title="负载率曲线"
              chart-type="loadRate"
              size="small"
            />
            <ChartButton
              text="有功功率"
              chart-title="有功功率曲线"
              chart-type="power"
              size="default"
            />
            <ChartButton
              text="绕组温度"
              chart-title="绕组温度曲线"
              chart-type="temperature"
              size="large"
            />
          </div>
        </div>
      </ElCard>

      <!-- 程序化控制 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">程序化控制</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <ElButton @click="openChart" type="primary">
              程序化打开图表
            </ElButton>
            <ElButton @click="closeChart" type="default">
              程序化关闭图表
            </ElButton>
          </div>

          <div class="text-sm text-gray-600">
            通过组件引用可以程序化控制图表弹窗的打开和关闭
          </div>
        </div>
      </ElCard>

      <!-- 自定义样式 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">自定义样式</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <ChartButton
              text="自定义按钮"
              chart-title="自定义图表"
              button-class="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
            />
            <ChartButton
              text="禁用状态"
              chart-title="禁用图表"
              :disabled="true"
            />
          </div>
        </div>
      </ElCard>

      <!-- 变压器监控样式 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">变压器监控样式</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <ChartButton
              text="负载率分析"
              chart-title="10kV变压器负载率曲线"
              chart-type="loadRate"
              size="default"
              button-class="bg-accent-foreground rounded-md"
            />
          </div>

          <div class="text-sm text-gray-600">
            这是在变压器监控页面中使用的样式配置
          </div>
        </div>
      </ElCard>
    </div>
  </Page>
</template>
